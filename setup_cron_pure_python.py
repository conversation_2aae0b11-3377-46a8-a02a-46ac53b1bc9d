#!/usr/bin/env python3

import os
import subprocess
import sys
from datetime import datetime

def log_message(message):
    """打印带时间戳的消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def get_script_dir():
    """获取脚本所在目录"""
    return os.path.dirname(os.path.abspath(__file__))

def get_python_path():
    """获取 Python3 的完整路径"""
    try:
        result = subprocess.run(["which", "python3"], capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        log_message("错误: 未找到 python3")
        return None

def backup_crontab():
    """备份当前的 crontab"""
    try:
        result = subprocess.run(["crontab", "-l"], capture_output=True, text=True)
        if result.returncode == 0:
            backup_file = f"crontab_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(backup_file, "w") as f:
                f.write(result.stdout)
            log_message(f"已备份当前 crontab 到: {backup_file}")
            return result.stdout
        else:
            log_message("当前没有 crontab 配置")
            return ""
    except Exception as e:
        log_message(f"备份 crontab 失败: {e}")
        return ""

def setup_cron():
    """设置 cron 任务"""
    log_message("=== 设置 Claude Code Python 自动激活定时任务 ===")
    
    # 获取路径信息
    script_dir = get_script_dir()
    python_path = get_python_path()
    
    if not python_path:
        return False
    
    python_script = os.path.join(script_dir, "activate_claude.py")
    
    # 检查 Python 脚本是否存在
    if not os.path.exists(python_script):
        log_message(f"错误: Python 脚本不存在: {python_script}")
        return False
    
    log_message(f"使用 Python: {python_path}")
    log_message(f"脚本路径: {python_script}")
    
    # 确保脚本有执行权限
    os.chmod(python_script, 0o755)
    
    # 备份当前 crontab
    current_crontab = backup_crontab()
    
    # 移除旧的 Claude 相关任务
    lines = current_crontab.split('\n')
    filtered_lines = [line for line in lines if 'activate_claude' not in line and 'Claude Code' not in line]
    
    # 添加新的 cron 任务
    new_cron_lines = [
        "",
        "# Claude Code Python 自动激活任务",
        "# 每天 7:00, 12:00, 17:00 激活 Claude Code",
        "# 直接调用 Python 脚本，稳定可靠",
        f"0 7 * * * cd {script_dir} && {python_path} {python_script} >> $HOME/claude_cron.log 2>&1",
        f"0 12 * * * cd {script_dir} && {python_path} {python_script} >> $HOME/claude_cron.log 2>&1", 
        f"0 17 * * * cd {script_dir} && {python_path} {python_script} >> $HOME/claude_cron.log 2>&1",
        ""
    ]
    
    # 合并所有行
    all_lines = filtered_lines + new_cron_lines
    new_crontab = '\n'.join(all_lines)
    
    # 写入新的 crontab
    try:
        process = subprocess.Popen(["crontab", "-"], stdin=subprocess.PIPE, text=True)
        process.communicate(input=new_crontab)
        
        if process.returncode == 0:
            log_message("✅ Cron 任务设置成功！")
            return True
        else:
            log_message("❌ 设置 cron 任务失败")
            return False
            
    except Exception as e:
        log_message(f"❌ 设置 cron 任务时发生错误: {e}")
        return False

def show_cron_status():
    """显示当前的 cron 任务状态"""
    try:
        result = subprocess.run(["crontab", "-l"], capture_output=True, text=True)
        if result.returncode == 0:
            log_message("当前的 cron 任务:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'activate_claude' in line or 'Claude Code' in line or (line.strip() and not line.startswith('#')):
                    print(f"  {line}")
        else:
            log_message("没有 cron 任务")
    except Exception as e:
        log_message(f"查看 cron 任务失败: {e}")

def main():
    """主函数"""
    print("Claude Code Python 定时任务设置工具")
    print("=" * 50)
    
    if setup_cron():
        print("\n🎉 设置完成！")
        print("\n已设置的激活时间:")
        print("- 每天 07:00")
        print("- 每天 12:00") 
        print("- 每天 17:00")
        print(f"\n日志文件:")
        print(f"- 激活日志: ~/claude_activation.log")
        print(f"- Cron 日志: ~/claude_cron.log")
        print("\n查看 cron 任务:")
        print("  crontab -l")
        print("\n手动测试:")
        print("  python3 activate_claude.py")
        
        print("\n" + "=" * 50)
        show_cron_status()
        
    else:
        print("\n💥 设置失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
