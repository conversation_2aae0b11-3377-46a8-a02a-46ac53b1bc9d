#!/usr/bin/env python3

import subprocess
import time
import os
import sys
from datetime import datetime

# 配置
LOG_FILE = os.path.expanduser("~/claude_activation.log")
ACTIVATION_MESSAGE = "hello"
STARTUP_DELAY = 10  # Claude 启动延时
RESPONSE_TIMEOUT = 15  # 等待响应超时

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    # 写入日志文件
    try:
        with open(LOG_FILE, "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
    except Exception as e:
        print(f"写入日志失败: {e}")

def activate_claude():
    """激活 Claude Code 并验证响应"""
    log_message("=== Claude Code Python 激活脚本启动 ===")
    log_message(f"当前时间: {datetime.now().strftime('%H:%M')}")
    
    # 检查 claude 命令是否可用
    try:
        subprocess.run(["which", "claude"], check=True, capture_output=True)
        log_message("✅ claude 命令已找到")
    except subprocess.CalledProcessError:
        log_message("❌ claude 命令未找到，请确保 Claude Code 已正确安装")
        return False
    
    try:
        log_message("启动 Claude Code...")
        
        # 启动 claude 进程
        process = subprocess.Popen(
            ["claude"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        log_message(f"等待 {STARTUP_DELAY} 秒让 Claude Code 完全启动...")
        time.sleep(STARTUP_DELAY)
        
        # 发送 hello 消息
        log_message(f"发送激活消息: '{ACTIVATION_MESSAGE}'")
        process.stdin.write(ACTIVATION_MESSAGE + "\n")
        process.stdin.flush()
        
        # 等待响应
        log_message(f"等待 Claude 响应 (最多 {RESPONSE_TIMEOUT} 秒)...")
        
        # 使用 communicate 获取输出，设置超时
        try:
            stdout, stderr = process.communicate(timeout=RESPONSE_TIMEOUT)
            
            log_message("Claude 原始输出:")
            log_message(f"STDOUT: {stdout}")
            if stderr:
                log_message(f"STDERR: {stderr}")
            
            # 检查输出是否包含有效响应
            if stdout and len(stdout.strip()) > 0:
                # 检查是否包含有意义的响应
                response_lower = stdout.lower()
                if any(keyword in response_lower for keyword in 
                       ["hello", "hi", "claude", "assistant", "help", "how", "can", "what", "i"]):
                    log_message("✅ Claude Code 激活成功 - 检测到有效响应")
                    return True
                elif len(stdout.strip()) > 10:
                    log_message("✅ Claude Code 激活成功 - 检测到文本响应")
                    return True
                else:
                    log_message(f"⚠️  Claude Code 响应较短: '{stdout.strip()}'")
                    return False
            else:
                log_message("❌ Claude Code 无响应")
                return False
                
        except subprocess.TimeoutExpired:
            log_message("❌ Claude Code 响应超时")
            process.kill()
            return False
            
    except Exception as e:
        log_message(f"❌ 激活过程中发生错误: {e}")
        return False
    
    finally:
        # 确保进程被清理
        try:
            if process.poll() is None:
                process.terminate()
                time.sleep(1)
                if process.poll() is None:
                    process.kill()
        except:
            pass

def activate_claude_simple():
    """简化版激活方法 - 使用 echo 管道"""
    log_message("使用简化方式激活 Claude Code...")
    
    try:
        # 使用 echo 管道发送命令
        cmd = f'echo "{ACTIVATION_MESSAGE}" | claude'
        
        log_message(f"执行命令: {cmd}")
        
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=20
        )
        
        log_message(f"返回码: {result.returncode}")
        log_message(f"输出: {result.stdout}")
        if result.stderr:
            log_message(f"错误: {result.stderr}")
        
        # 检查是否有输出
        if result.stdout and len(result.stdout.strip()) > 0:
            log_message("✅ Claude Code 简化激活成功")
            return True
        else:
            log_message("❌ Claude Code 简化激活失败 - 无输出")
            return False
            
    except subprocess.TimeoutExpired:
        log_message("❌ Claude Code 简化激活超时")
        return False
    except Exception as e:
        log_message(f"❌ 简化激活错误: {e}")
        return False

def main():
    """主函数"""
    log_message("开始验证激活 Claude Code...")
    
    # 首先尝试交互式方法
    if activate_claude():
        log_message("🎉 Claude Code 验证激活成功！")
        return True
    
    log_message("交互式方法失败，尝试简化方法...")
    
    # 备用方案：简化方法
    if activate_claude_simple():
        log_message("🎉 Claude Code 简化激活成功！")
        return True
    
    log_message("💥 所有激活方法都失败了！")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
