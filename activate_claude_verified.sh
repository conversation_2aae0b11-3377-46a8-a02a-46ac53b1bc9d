#!/bin/bash

# Claude Code 验证激活脚本
# 确保 Claude 真正响应才算激活成功

# 配置
LOG_FILE="$HOME/claude_activation.log"
ACTIVATION_MESSAGE="hello"
TIMEOUT=30

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# 使用 expect 进行验证激活
activate_claude_with_expect() {
    log_message "使用 expect 激活并验证 Claude Code..."
    
    # 检查 expect 是否可用
    if ! command -v expect &> /dev/null; then
        log_message "错误: 需要安装 expect 工具"
        log_message "macOS: brew install expect"
        log_message "Ubuntu: sudo apt-get install expect"
        return 1
    fi
    
    # 创建临时文件来捕获输出
    local temp_output=$(mktemp)
    
    # 使用 expect 自动化交互
    expect << EOF > "$temp_output" 2>&1
set timeout $TIMEOUT
log_user 1

spawn claude
expect {
    ">" {
        send "$ACTIVATION_MESSAGE\r"
        expect {
            -re ".+" {
                set response \$expect_out(buffer)
                puts "\n=== CLAUDE RESPONSE START ==="
                puts \$response
                puts "=== CLAUDE RESPONSE END ==="
                exp_continue
            }
            timeout {
                puts "\n=== TIMEOUT WAITING FOR RESPONSE ==="
                exit 1
            }
        }
    }
    "Welcome" {
        send "$ACTIVATION_MESSAGE\r"
        exp_continue
    }
    timeout {
        puts "\n=== TIMEOUT STARTING CLAUDE ==="
        exit 1
    }
    eof {
        puts "\n=== CLAUDE EXITED UNEXPECTEDLY ==="
        exit 1
    }
}

# 等待更多输出
sleep 3

# 发送退出命令
send "\x03"
expect eof
EOF

    local expect_exit_code=$?
    local claude_output=$(cat "$temp_output")
    rm -f "$temp_output"
    
    # 记录完整输出用于调试
    log_message "Claude 完整输出:"
    echo "$claude_output" >> "$LOG_FILE"
    
    # 分析输出
    if [ $expect_exit_code -eq 0 ]; then
        # 检查是否有响应内容
        if echo "$claude_output" | grep -q "CLAUDE RESPONSE START"; then
            local response_content=$(echo "$claude_output" | sed -n '/CLAUDE RESPONSE START/,/CLAUDE RESPONSE END/p')
            
            # 检查响应是否包含有意义的内容
            if echo "$response_content" | grep -i -E "(hello|hi|claude|assistant|help|how|can|what)" > /dev/null; then
                log_message "✅ Claude Code 激活成功 - 检测到有效响应"
                return 0
            elif echo "$response_content" | grep -E "[a-zA-Z]{3,}" > /dev/null; then
                log_message "✅ Claude Code 激活成功 - 检测到文本响应"
                return 0
            else
                log_message "⚠️  Claude Code 启动但响应内容可疑"
                log_message "响应内容: $response_content"
                return 1
            fi
        else
            log_message "❌ 未检测到 Claude 响应"
            return 1
        fi
    else
        if echo "$claude_output" | grep -q "TIMEOUT"; then
            log_message "❌ Claude Code 激活超时"
        else
            log_message "❌ Claude Code 激活失败"
        fi
        return 1
    fi
}

# 简单的激活验证（备用方案）
activate_claude_simple_verify() {
    log_message "使用简单方式验证激活 Claude Code..."
    
    # 创建临时脚本
    local temp_script=$(mktemp)
    cat > "$temp_script" << 'EOF'
#!/bin/bash
echo "Starting claude..."
timeout 20 bash -c '
    echo "hello" | claude 2>&1
' || echo "TIMEOUT_OR_ERROR"
EOF
    
    chmod +x "$temp_script"
    local output=$("$temp_script" 2>&1)
    rm -f "$temp_script"
    
    log_message "Claude 输出: $output"
    
    # 检查输出
    if echo "$output" | grep -q "TIMEOUT_OR_ERROR"; then
        log_message "❌ Claude Code 响应超时"
        return 1
    elif echo "$output" | grep -i -E "(hello|hi|claude|assistant|help|error|command)" > /dev/null; then
        log_message "✅ Claude Code 激活成功 - 检测到响应"
        return 0
    else
        log_message "⚠️  Claude Code 响应异常: $output"
        return 1
    fi
}

# 主激活函数
activate_claude() {
    log_message "开始验证激活 Claude Code..."
    
    # 首先尝试使用 expect
    if activate_claude_with_expect; then
        return 0
    fi
    
    log_message "expect 方式失败，尝试简单验证方式..."
    
    # 备用方案
    if activate_claude_simple_verify; then
        return 0
    fi
    
    log_message "❌ 所有激活验证方式都失败了"
    return 1
}

# 主函数
main() {
    log_message "=== Claude Code 验证激活脚本启动 ==="
    
    # 记录当前时间
    current_time=$(date '+%H:%M')
    log_message "当前时间: $current_time"
    
    # 检查 claude 命令是否可用
    if ! command -v claude &> /dev/null; then
        log_message "错误: claude 命令未找到，请确保 Claude Code 已正确安装"
        exit 1
    fi
    
    # 执行激活验证
    if activate_claude; then
        log_message "🎉 Claude Code 验证激活成功！"
        exit 0
    else
        log_message "💥 Claude Code 验证激活失败！"
        exit 1
    fi
}

# 执行主函数
main "$@"
