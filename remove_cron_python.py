#!/usr/bin/env python3

import subprocess
import sys
from datetime import datetime

def log_message(message):
    """打印带时间戳的消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def backup_crontab():
    """备份当前的 crontab"""
    try:
        result = subprocess.run(["crontab", "-l"], capture_output=True, text=True)
        if result.returncode == 0:
            backup_file = f"crontab_backup_before_removal_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(backup_file, "w") as f:
                f.write(result.stdout)
            log_message(f"已备份当前 crontab 到: {backup_file}")
            return result.stdout
        else:
            log_message("当前没有 crontab 配置")
            return ""
    except Exception as e:
        log_message(f"备份 crontab 失败: {e}")
        return ""

def remove_claude_cron():
    """移除 Claude Code 相关的 cron 任务"""
    log_message("=== 移除 Claude Code 自动激活定时任务 ===")
    
    # 备份当前 crontab
    current_crontab = backup_crontab()
    
    if not current_crontab:
        log_message("没有 cron 任务需要移除")
        return True
    
    # 过滤掉 Claude 相关的行
    lines = current_crontab.split('\n')
    filtered_lines = []
    removed_count = 0
    
    skip_next = False
    for line in lines:
        if skip_next and line.strip() == "":
            skip_next = False
            continue
            
        if ('activate_claude' in line or 
            'Claude Code' in line or 
            line.strip().startswith('# Claude Code')):
            removed_count += 1
            if line.strip().startswith('#'):
                skip_next = True  # 跳过注释后的空行
            continue
        else:
            filtered_lines.append(line)
            skip_next = False
    
    # 移除末尾的空行
    while filtered_lines and filtered_lines[-1].strip() == "":
        filtered_lines.pop()
    
    new_crontab = '\n'.join(filtered_lines)
    if new_crontab and not new_crontab.endswith('\n'):
        new_crontab += '\n'
    
    # 写入新的 crontab
    try:
        process = subprocess.Popen(["crontab", "-"], stdin=subprocess.PIPE, text=True)
        process.communicate(input=new_crontab)
        
        if process.returncode == 0:
            log_message(f"✅ 成功移除 {removed_count} 个 Claude Code 相关任务")
            return True
        else:
            log_message("❌ 移除 cron 任务失败")
            return False
            
    except Exception as e:
        log_message(f"❌ 移除 cron 任务时发生错误: {e}")
        return False

def show_remaining_cron():
    """显示剩余的 cron 任务"""
    try:
        result = subprocess.run(["crontab", "-l"], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            log_message("剩余的 cron 任务:")
            print(result.stdout)
        else:
            log_message("没有剩余的 cron 任务")
    except Exception as e:
        log_message(f"查看 cron 任务失败: {e}")

def main():
    """主函数"""
    print("Claude Code Python 定时任务移除工具")
    print("=" * 50)
    
    if remove_claude_cron():
        print("\n🎉 移除完成！")
        print("\n" + "=" * 50)
        show_remaining_cron()
    else:
        print("\n💥 移除失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
