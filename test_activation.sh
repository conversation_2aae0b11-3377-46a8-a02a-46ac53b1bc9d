#!/bin/bash

# 测试 Claude Code 激活脚本

echo "=== Claude Code 激活测试 ==="
echo ""

# 检查 claude 命令
echo "1. 检查 claude 命令是否可用..."
if command -v claude &> /dev/null; then
    echo "✅ claude 命令已找到: $(which claude)"
else
    echo "❌ claude 命令未找到"
    echo "请确保 Claude Code 已正确安装并在 PATH 中"
    exit 1
fi

echo ""

# 检查操作系统
echo "2. 检查操作系统..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "✅ 检测到 macOS 系统"
    OS="macos"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "✅ 检测到 Linux 系统"
    OS="linux"
    
    # 检查 xdotool
    if ! command -v xdotool &> /dev/null; then
        echo "⚠️  警告: 未找到 xdotool，Linux 自动化可能无法工作"
        echo "请安装: sudo apt-get install xdotool (Ubuntu/Debian)"
        echo "或: sudo yum install xdotool (CentOS/RHEL)"
    fi
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

echo ""

# 测试激活脚本
echo "3. 测试激活脚本..."
echo "即将运行验证激活脚本，会检查 Claude Code 是否真正响应"
echo ""

# 检查 expect 是否可用
if command -v expect &> /dev/null; then
    echo "✅ expect 工具已安装，将使用完整验证"
else
    echo "⚠️  expect 工具未安装，将使用简化验证"
    echo "建议安装 expect: brew install expect (macOS) 或 sudo apt-get install expect (Linux)"
fi

echo ""
read -p "按回车键继续测试，或 Ctrl+C 取消..."

# 给新脚本执行权限
chmod +x activate_claude_verified.sh
chmod +x activate_claude_macos.sh

# 根据操作系统选择脚本
echo ""
echo "=== 运行验证激活脚本 ==="
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "使用 macOS 专用脚本..."
    ./activate_claude_macos.sh
else
    echo "使用通用脚本..."
    ./activate_claude_verified.sh
fi

echo ""
echo "测试完成！"
echo ""
echo "如果测试成功，你可以运行以下命令设置定时任务："
echo "  ./setup_cron.sh"
echo ""
echo "查看日志："
echo "  tail -f ~/claude_activation.log"
