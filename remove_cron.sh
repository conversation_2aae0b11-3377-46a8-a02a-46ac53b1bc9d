#!/bin/bash

# 移除 Claude Code 自动激活的 cron 任务

echo "移除 Claude Code 自动激活定时任务..."

# 备份当前的 crontab
echo "备份当前 crontab..."
crontab -l > crontab_backup_before_removal_$(date +%Y%m%d_%H%M%S).txt 2>/dev/null

# 创建临时文件
TEMP_CRON=$(mktemp)

# 移除包含 "activate_claude" 的行
crontab -l 2>/dev/null | grep -v "activate_claude" > "$TEMP_CRON"

# 安装新的 crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm "$TEMP_CRON"

echo "Claude Code 自动激活任务已移除！"
echo ""
echo "当前剩余的 cron 任务："
crontab -l 2>/dev/null || echo "没有 cron 任务"
