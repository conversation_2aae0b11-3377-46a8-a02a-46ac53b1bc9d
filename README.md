# Claude Code 自动激活脚本

这个脚本用于自动激活 Claude Code，以便合理管理每5小时的 token 额度窗口。

## 功能

- 自动在指定时间（7:00, 12:00, 17:00）激活 Claude Code
- 支持 macOS 和 Linux 系统
- 自动记录激活日志
- 简单的安装和卸载

## 文件说明

- `activate_claude_simple.sh` - 主要的激活脚本
- `setup_cron.sh` - 设置定时任务
- `remove_cron.sh` - 移除定时任务
- `test_activation.sh` - 测试激活功能
- `activate_claude.sh` - 使用 expect 的高级版本（需要安装 expect）

## 使用方法

### 1. 准备工作

确保 Claude Code 已正确安装并且 `claude` 命令在 PATH 中可用：

```bash
# 测试 claude 命令
claude --version
```

### 2. 测试激活功能

首先测试激活脚本是否正常工作：

```bash
# 给脚本添加执行权限
chmod +x *.sh

# 运行测试
./test_activation.sh
```

### 3. 设置定时任务

如果测试成功，设置自动激活：

```bash
./setup_cron.sh
```

这将设置以下定时任务：
- 每天 07:00 激活
- 每天 12:00 激活  
- 每天 17:00 激活

### 4. 查看日志

```bash
# 查看激活日志
tail -f ~/claude_activation.log

# 查看最近的激活记录
tail -20 ~/claude_activation.log
```

### 5. 移除定时任务

如果需要停止自动激活：

```bash
./remove_cron.sh
```

## 系统要求

### macOS
- 系统自带的 AppleScript 支持
- Terminal.app

### Linux
- `xdotool` 工具（用于自动化）
- `gnome-terminal` 或其他终端

安装 xdotool：
```bash
# Ubuntu/Debian
sudo apt-get install xdotool

# CentOS/RHEL
sudo yum install xdotool

# Arch Linux
sudo pacman -S xdotool
```

## 故障排除

### 1. claude 命令未找到
确保 Claude Code 已正确安装，并且 `claude` 命令在 PATH 中。

### 2. 权限问题
确保脚本有执行权限：
```bash
chmod +x *.sh
```

### 3. cron 任务不执行
检查 cron 服务是否运行：
```bash
# macOS
sudo launchctl list | grep cron

# Linux
sudo systemctl status cron
```

### 4. 查看 cron 日志
```bash
# macOS
tail -f /var/log/system.log | grep cron

# Linux
tail -f /var/log/cron
```

## 自定义

### 修改激活时间
编辑 `setup_cron.sh` 文件中的时间设置：
```bash
# 格式: 分钟 小时 日 月 星期
0 7 * * *   # 每天 7:00
0 12 * * *  # 每天 12:00
0 17 * * *  # 每天 17:00
```

### 修改激活消息
编辑 `activate_claude_simple.sh` 文件中的 `ACTIVATION_MESSAGE` 变量。

## 注意事项

1. 确保在激活时间点电脑是开机状态
2. 定时任务在用户登录状态下才会执行
3. 如果 Claude Code 需要特殊的启动参数，请修改脚本中的命令
4. 建议定期检查日志文件确保激活正常工作
