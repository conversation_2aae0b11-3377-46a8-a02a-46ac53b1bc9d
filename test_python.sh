#!/bin/bash

# 测试 Python 版本的 Claude Code 激活脚本

echo "=== Claude Code Python 激活测试 ==="
echo ""

# 检查 Python
echo "1. 检查 Python 环境..."
if command -v python3 &> /dev/null; then
    echo "✅ Python3 已找到: $(which python3)"
    echo "   版本: $(python3 --version)"
else
    echo "❌ Python3 未找到"
    exit 1
fi

echo ""

# 检查 claude 命令
echo "2. 检查 claude 命令是否可用..."
if command -v claude &> /dev/null; then
    echo "✅ claude 命令已找到: $(which claude)"
else
    echo "❌ claude 命令未找到"
    echo "请确保 Claude Code 已正确安装并在 PATH 中"
    exit 1
fi

echo ""

# 给 Python 脚本执行权限
chmod +x activate_claude.py

# 测试 Python 激活脚本
echo "3. 测试 Python 激活脚本..."
echo "即将运行 Python 激活脚本，会自动发送 hello 消息并验证响应"
echo ""
read -p "按回车键继续测试，或 Ctrl+C 取消..."

echo ""
echo "=== 运行 Python 激活脚本 ==="
python3 activate_claude.py

echo ""
echo "测试完成！"
echo ""
echo "如果测试成功，你可以运行以下命令设置定时任务："
echo "  ./setup_cron_python.sh"
echo ""
echo "查看日志："
echo "  tail -f ~/claude_activation.log"
