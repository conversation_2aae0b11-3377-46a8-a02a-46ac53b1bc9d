#!/bin/bash

# Claude Code macOS 专用激活验证脚本

# 配置
LOG_FILE="$HOME/claude_activation.log"
ACTIVATION_MESSAGE="hello"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 使用 AppleScript 和 expect 进行验证激活
activate_claude_macos() {
    log_message "使用 macOS 方式激活并验证 Claude Code..."
    
    # 创建临时文件
    local temp_output=$(mktemp)
    local temp_expect_script=$(mktemp)
    
    # 创建 expect 脚本
    cat > "$temp_expect_script" << 'EOF'
#!/usr/bin/expect -f
set timeout 30
log_user 0

# 启动 claude
spawn claude
expect {
    ">" {
        # 发送 hello 消息
        send "hello\r"
        
        # 等待响应
        expect {
            -re ".+" {
                set response $expect_out(buffer)
                puts "RESPONSE_START"
                puts $response
                puts "RESPONSE_END"
                
                # 继续等待更多输出
                expect {
                    -re ".+" {
                        puts "ADDITIONAL_OUTPUT"
                        puts $expect_out(buffer)
                    }
                    timeout { }
                }
            }
            timeout {
                puts "NO_RESPONSE_TIMEOUT"
                exit 1
            }
        }
    }
    "Welcome" {
        send "hello\r"
        exp_continue
    }
    timeout {
        puts "STARTUP_TIMEOUT"
        exit 1
    }
    eof {
        puts "UNEXPECTED_EOF"
        exit 1
    }
}

# 发送 Ctrl+C 退出
send "\x03"
expect eof
exit 0
EOF

    chmod +x "$temp_expect_script"
    
    # 运行 expect 脚本
    "$temp_expect_script" > "$temp_output" 2>&1
    local expect_exit_code=$?
    
    # 读取输出
    local claude_output=$(cat "$temp_output")
    
    # 清理临时文件
    rm -f "$temp_expect_script" "$temp_output"
    
    # 记录输出用于调试
    log_message "Claude 原始输出:"
    echo "$claude_output" >> "$LOG_FILE"
    
    # 分析结果
    if [ $expect_exit_code -eq 0 ]; then
        if echo "$claude_output" | grep -q "RESPONSE_START"; then
            # 提取响应内容
            local response_content=$(echo "$claude_output" | sed -n '/RESPONSE_START/,/RESPONSE_END/p' | grep -v "RESPONSE_")
            
            log_message "Claude 响应内容: $response_content"
            
            # 检查响应是否有意义
            if echo "$response_content" | grep -i -E "(hello|hi|claude|assistant|help|how|can|what|I)" > /dev/null; then
                log_message "✅ Claude Code 激活成功 - 检测到有效响应"
                return 0
            elif [ ${#response_content} -gt 10 ]; then
                log_message "✅ Claude Code 激活成功 - 检测到文本响应"
                return 0
            else
                log_message "⚠️  Claude Code 响应内容较短: '$response_content'"
                return 1
            fi
        else
            log_message "❌ 未检测到 Claude 响应"
            return 1
        fi
    else
        if echo "$claude_output" | grep -q "STARTUP_TIMEOUT"; then
            log_message "❌ Claude Code 启动超时"
        elif echo "$claude_output" | grep -q "NO_RESPONSE_TIMEOUT"; then
            log_message "❌ Claude Code 无响应超时"
        else
            log_message "❌ Claude Code 激活失败: $claude_output"
        fi
        return 1
    fi
}

# 简单的管道测试方法
activate_claude_pipe_test() {
    log_message "使用管道方式测试 Claude Code..."
    
    # 创建临时文件
    local temp_output=$(mktemp)
    
    # 使用管道发送命令并捕获输出
    (
        echo "hello"
        sleep 2
        echo "exit"
    ) | claude > "$temp_output" 2>&1 &
    
    local claude_pid=$!
    
    # 等待最多 20 秒
    local count=0
    while [ $count -lt 20 ] && kill -0 $claude_pid 2>/dev/null; do
        sleep 1
        count=$((count + 1))
    done
    
    # 如果进程还在运行，杀掉它
    if kill -0 $claude_pid 2>/dev/null; then
        kill $claude_pid 2>/dev/null
        sleep 1
        kill -9 $claude_pid 2>/dev/null
    fi
    
    # 读取输出
    local claude_output=$(cat "$temp_output")
    rm -f "$temp_output"
    
    log_message "Claude 管道输出: $claude_output"
    
    # 检查输出
    if [ ${#claude_output} -gt 5 ] && echo "$claude_output" | grep -v "^$" > /dev/null; then
        log_message "✅ Claude Code 管道测试成功"
        return 0
    else
        log_message "❌ Claude Code 管道测试失败"
        return 1
    fi
}

# 主激活函数
activate_claude() {
    log_message "开始验证激活 Claude Code..."
    
    # 检查 expect 是否可用
    if command -v expect &> /dev/null; then
        log_message "使用 expect 进行交互式验证..."
        if activate_claude_macos; then
            return 0
        fi
        log_message "expect 方式失败，尝试管道方式..."
    else
        log_message "expect 未安装，使用管道方式..."
    fi
    
    # 备用方案：管道测试
    if activate_claude_pipe_test; then
        return 0
    fi
    
    log_message "❌ 所有验证方式都失败了"
    return 1
}

# 主函数
main() {
    log_message "=== Claude Code macOS 验证激活脚本启动 ==="
    
    # 记录当前时间
    current_time=$(date '+%H:%M')
    log_message "当前时间: $current_time"
    
    # 检查 claude 命令是否可用
    if ! command -v claude &> /dev/null; then
        log_message "错误: claude 命令未找到，请确保 Claude Code 已正确安装"
        exit 1
    fi
    
    # 执行激活验证
    if activate_claude; then
        log_message "🎉 Claude Code 验证激活成功！"
        exit 0
    else
        log_message "💥 Claude Code 验证激活失败！"
        exit 1
    fi
}

# 执行主函数
main "$@"
