#!/bin/bash

# Claude Code 自动激活脚本
# 用于在指定时间激活 Claude Code 以管理 token 额度

# 配置
LOG_FILE="$HOME/claude_activation.log"
CLAUDE_COMMAND="claude"
ACTIVATION_MESSAGE="hello"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查 claude 命令是否可用
check_claude_available() {
    if ! command -v claude &> /dev/null; then
        log_message "错误: claude 命令未找到，请确保 Claude Code 已正确安装"
        exit 1
    fi
}

# 激活 Claude Code
activate_claude() {
    log_message "开始激活 Claude Code..."
    
    # 检查 claude 是否可用
    check_claude_available
    
    # 使用 expect 来自动化交互过程
    expect << EOF
set timeout 30
spawn $CLAUDE_COMMAND
expect {
    ">" {
        send "$ACTIVATION_MESSAGE\r"
        expect {
            -re ".*" {
                send_user "Claude Code 激活成功\n"
            }
            timeout {
                send_user "激活超时\n"
                exit 1
            }
        }
    }
    timeout {
        send_user "启动 Claude Code 超时\n"
        exit 1
    }
}

# 等待一下然后退出
sleep 2
send "\x03"
expect eof
EOF

    if [ $? -eq 0 ]; then
        log_message "Claude Code 激活成功"
    else
        log_message "Claude Code 激活失败"
        return 1
    fi
}

# 主函数
main() {
    log_message "=== Claude Code 自动激活脚本启动 ==="
    
    # 记录当前时间
    current_time=$(date '+%H:%M')
    log_message "当前时间: $current_time"
    
    # 执行激活
    activate_claude
    
    log_message "=== 脚本执行完成 ==="
}

# 执行主函数
main "$@"
