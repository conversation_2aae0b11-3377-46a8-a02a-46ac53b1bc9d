#!/bin/bash

# Claude Code 简单自动激活脚本
# 使用 AppleScript (macOS) 或其他方式来自动化

# 配置
LOG_FILE="$HOME/claude_activation.log"
ACTIVATION_MESSAGE="hello"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# macOS 版本的激活函数
activate_claude_macos() {
    log_message "使用 macOS AppleScript 激活 Claude Code..."
    
    osascript << EOF
tell application "Terminal"
    activate
    do script "claude"
    delay 3
    
    -- 获取最前面的窗口
    set frontWindow to front window
    
    -- 发送 hello 命令
    do script "$ACTIVATION_MESSAGE" in frontWindow
    delay 2
    
    -- 发送回车
    do script "" in frontWindow
    delay 1
    
    -- 关闭终端窗口（可选）
    -- close frontWindow
end tell
EOF

    if [ $? -eq 0 ]; then
        log_message "Claude Code 激活成功 (macOS)"
        return 0
    else
        log_message "Claude Code 激活失败 (macOS)"
        return 1
    fi
}

# Linux 版本的激活函数
activate_claude_linux() {
    log_message "使用 Linux 方式激活 Claude Code..."
    
    # 尝试使用 xdotool 或其他工具
    if command -v xdotool &> /dev/null; then
        # 打开新的终端窗口
        gnome-terminal -- bash -c "claude" &
        sleep 3
        
        # 发送 hello 命令
        xdotool type "$ACTIVATION_MESSAGE"
        xdotool key Return
        
        log_message "Claude Code 激活成功 (Linux)"
        return 0
    else
        log_message "错误: 需要安装 xdotool 工具"
        return 1
    fi
}

# 通用激活函数
activate_claude() {
    local os=$(detect_os)
    
    case $os in
        "macos")
            activate_claude_macos
            ;;
        "linux")
            activate_claude_linux
            ;;
        *)
            log_message "错误: 不支持的操作系统 ($OSTYPE)"
            return 1
            ;;
    esac
}

# 主函数
main() {
    log_message "=== Claude Code 自动激活脚本启动 ==="
    
    # 记录当前时间
    current_time=$(date '+%H:%M')
    log_message "当前时间: $current_time"
    
    # 检查 claude 命令是否可用
    if ! command -v claude &> /dev/null; then
        log_message "错误: claude 命令未找到，请确保 Claude Code 已正确安装"
        exit 1
    fi
    
    # 执行激活
    activate_claude
    
    log_message "=== 脚本执行完成 ==="
}

# 执行主函数
main "$@"
