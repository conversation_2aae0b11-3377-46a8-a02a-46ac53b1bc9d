#!/bin/bash

# Claude Code 简单自动激活脚本
# 使用 AppleScript (macOS) 或其他方式来自动化

# 配置
LOG_FILE="$HOME/claude_activation.log"
ACTIVATION_MESSAGE="hello"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# macOS 版本的激活函数
activate_claude_macos() {
    log_message "使用 macOS AppleScript 激活 Claude Code..."

    # 创建临时文件来捕获输出
    local temp_output=$(mktemp)
    local temp_script=$(mktemp)

    # 创建 AppleScript 来捕获终端输出
    cat > "$temp_script" << 'EOF'
tell application "Terminal"
    activate
    set newTab to do script "claude"
    delay 5

    -- 发送 hello 命令
    do script "hello" in newTab
    delay 3

    -- 等待响应
    delay 5

    -- 获取终端内容
    set terminalContent to contents of newTab

    -- 将内容写入临时文件
    do shell script "echo " & quoted form of terminalContent & " > " & quoted form of POSIX path of (path to temporary items) & "claude_output.txt"

    -- 关闭标签页
    close newTab

    return terminalContent
end tell
EOF

    # 运行 AppleScript 并捕获输出
    local applescript_result
    applescript_result=$(osascript "$temp_script" 2>&1)
    local applescript_exit_code=$?

    # 清理临时脚本
    rm -f "$temp_script"

    # 检查 AppleScript 是否成功执行
    if [ $applescript_exit_code -ne 0 ]; then
        log_message "AppleScript 执行失败: $applescript_result"
        rm -f "$temp_output"
        return 1
    fi

    # 检查输出中是否包含 Claude 的响应
    log_message "检查 Claude Code 响应..."

    # 从系统临时目录读取输出
    local claude_output_file="/tmp/claude_output.txt"
    if [ -f "$claude_output_file" ]; then
        local claude_output=$(cat "$claude_output_file")
        rm -f "$claude_output_file"

        log_message "Claude 输出内容: $claude_output"

        # 检查是否包含 Claude 的典型响应
        if echo "$claude_output" | grep -i -E "(hello|hi|claude|assistant|help)" > /dev/null; then
            log_message "✅ Claude Code 激活成功 - 检测到有效响应"
            return 0
        else
            log_message "❌ Claude Code 可能未正确响应 - 未检测到预期的响应内容"
            return 1
        fi
    else
        log_message "❌ 无法获取 Claude Code 输出"
        return 1
    fi
}

# Linux 版本的激活函数
activate_claude_linux() {
    log_message "使用 Linux 方式激活 Claude Code..."

    # 检查必要工具
    if ! command -v xdotool &> /dev/null; then
        log_message "错误: 需要安装 xdotool 工具"
        return 1
    fi

    if ! command -v expect &> /dev/null; then
        log_message "错误: 需要安装 expect 工具"
        return 1
    fi

    # 创建临时文件来捕获输出
    local temp_output=$(mktemp)

    # 使用 expect 来自动化交互并捕获输出
    expect << EOF > "$temp_output" 2>&1
set timeout 30
spawn claude
expect {
    ">" {
        send "hello\r"
        expect {
            -re ".*" {
                set output \$expect_out(buffer)
                puts "CLAUDE_RESPONSE_START"
                puts \$output
                puts "CLAUDE_RESPONSE_END"
            }
            timeout {
                puts "TIMEOUT_WAITING_FOR_RESPONSE"
                exit 1
            }
        }
    }
    timeout {
        puts "TIMEOUT_STARTING_CLAUDE"
        exit 1
    }
}

# 等待一下然后退出
sleep 2
send "\x03"
expect eof
EOF

    local expect_exit_code=$?

    # 读取输出
    local claude_output=$(cat "$temp_output")
    rm -f "$temp_output"

    log_message "Claude 原始输出: $claude_output"

    # 检查是否成功
    if [ $expect_exit_code -eq 0 ] && echo "$claude_output" | grep "CLAUDE_RESPONSE_START" > /dev/null; then
        # 提取响应内容
        local response_content=$(echo "$claude_output" | sed -n '/CLAUDE_RESPONSE_START/,/CLAUDE_RESPONSE_END/p' | grep -v "CLAUDE_RESPONSE_")

        log_message "Claude 响应内容: $response_content"

        # 检查是否包含有效响应
        if echo "$response_content" | grep -i -E "(hello|hi|claude|assistant|help)" > /dev/null; then
            log_message "✅ Claude Code 激活成功 - 检测到有效响应"
            return 0
        else
            log_message "❌ Claude Code 可能未正确响应 - 未检测到预期的响应内容"
            return 1
        fi
    else
        log_message "❌ Claude Code 激活失败 - 无法获取响应"
        return 1
    fi
}

# 通用激活函数
activate_claude() {
    local os=$(detect_os)
    
    case $os in
        "macos")
            activate_claude_macos
            ;;
        "linux")
            activate_claude_linux
            ;;
        *)
            log_message "错误: 不支持的操作系统 ($OSTYPE)"
            return 1
            ;;
    esac
}

# 主函数
main() {
    log_message "=== Claude Code 自动激活脚本启动 ==="
    
    # 记录当前时间
    current_time=$(date '+%H:%M')
    log_message "当前时间: $current_time"
    
    # 检查 claude 命令是否可用
    if ! command -v claude &> /dev/null; then
        log_message "错误: claude 命令未找到，请确保 Claude Code 已正确安装"
        exit 1
    fi
    
    # 执行激活
    activate_claude
    
    log_message "=== 脚本执行完成 ==="
}

# 执行主函数
main "$@"
