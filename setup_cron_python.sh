#!/bin/bash

# 设置 Claude Code Python 自动激活的 cron 任务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/activate_claude.py"

echo "设置 Claude Code Python 自动激活定时任务..."

# 确保 Python 脚本有执行权限
chmod +x "$PYTHON_SCRIPT"

# 检查 Python3 是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: python3 未找到，请先安装 Python 3"
    exit 1
fi

# 获取 python3 的完整路径
PYTHON3_PATH=$(which python3)
echo "使用 Python: $PYTHON3_PATH"

# 备份当前的 crontab
echo "备份当前 crontab..."
crontab -l > crontab_backup_$(date +%Y%m%d_%H%M%S).txt 2>/dev/null || echo "当前没有 crontab 配置"

# 创建新的 cron 配置
echo "创建 cron 配置..."

# 临时文件
TEMP_CRON=$(mktemp)

# 保留现有的 cron 任务（如果有的话）
crontab -l 2>/dev/null | grep -v "activate_claude" > "$TEMP_CRON"

# 添加新的 cron 任务
cat >> "$TEMP_CRON" << EOF

# Claude Code Python 自动激活任务
# 每天 7:00, 12:00, 17:00 激活 Claude Code
0 7 * * * $PYTHON3_PATH $PYTHON_SCRIPT
0 12 * * * $PYTHON3_PATH $PYTHON_SCRIPT
0 17 * * * $PYTHON3_PATH $PYTHON_SCRIPT

EOF

# 安装新的 crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm "$TEMP_CRON"

echo "Cron 任务设置完成！"
echo ""
echo "已设置的激活时间："
echo "- 每天 07:00"
echo "- 每天 12:00" 
echo "- 每天 17:00"
echo ""
echo "使用的 Python 脚本: $PYTHON_SCRIPT"
echo "使用的 Python 解释器: $PYTHON3_PATH"
echo ""
echo "查看当前 cron 任务："
crontab -l | grep -A 5 -B 1 "Claude Code"
echo ""
echo "日志文件位置: $HOME/claude_activation.log"
echo ""
echo "如需取消定时任务，请运行: ./remove_cron.sh"
